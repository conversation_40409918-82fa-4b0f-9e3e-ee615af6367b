// FCM REST API v1 Service using Firebase Admin SDK
// Implementação seguindo: https://firebase.google.com/docs/reference/fcm/rest/v1/projects.messages

import admin from 'firebase-admin';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class FCMv1Service {
  constructor() {
    this.projectId = 'iconpal-cf925';
    this.developmentMode = false;
    this.initializeFirebaseAdmin();
  }

  /**
   * Inicializar Firebase Admin SDK
   */
  initializeFirebaseAdmin() {
    try {
      // Verificar se já foi inicializado
      if (admin.apps.length > 0) {
        console.log('✅ Firebase Admin already initialized');
        return;
      }

      // Caminho para o service account
      const serviceAccountPath = path.join(__dirname, '../firebase-service-account.json');

      // Verificar se o arquivo existe
      if (!fs.existsSync(serviceAccountPath)) {
        console.log('⚠️ Service account file not found, trying environment variables...');

        // Tentar usar variáveis de ambiente
        if (process.env.FIREBASE_SERVICE_ACCOUNT_KEY) {
          const serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY);
          admin.initializeApp({
            credential: admin.credential.cert(serviceAccount),
            projectId: this.projectId
          });
          console.log('✅ Firebase Admin initialized with environment variables');
        } else {
          // ✅ NOVO: Modo de desenvolvimento sem service account
          console.log('⚠️ No Firebase service account found - running in DEVELOPMENT MODE');
          console.log('📋 FCM notifications will be simulated (not actually sent)');
          this.developmentMode = true;
          
          // Não inicializar Firebase Admin em modo desenvolvimento
          return;
        }
      } else {
        // Usar arquivo service account
        const serviceAccount = JSON.parse(fs.readFileSync(serviceAccountPath, 'utf8'));
        admin.initializeApp({
          credential: admin.credential.cert(serviceAccount),
          projectId: this.projectId
        });
        console.log('✅ Firebase Admin initialized with service account file');
      }

    } catch (error) {
      console.error('❌ Error initializing Firebase Admin:', error);
      console.log('⚠️ Falling back to DEVELOPMENT MODE - FCM will be simulated');
      this.developmentMode = true;
    }
  }

  /**
   * Obter instância do Firebase Messaging
   */
  getMessaging() {
    if (this.developmentMode) {
      throw new Error('Firebase Admin not available in development mode');
    }
    return admin.messaging();
  }

  /**
   * Enviar notificação usando Firebase Admin SDK
   * @param {string} token - FCM registration token
   * @param {Object} notification - Dados da notificação
   * @param {Object} data - Dados customizados (opcional)
   * @param {Object} webpush - Configurações específicas para web (opcional)
   */
  async sendNotification(token, notification, data = {}, webpush = {}) {
    try {
      console.log('📱 Enviando notificação via Firebase Admin SDK...');
      console.log('🎯 Target token:', token.substring(0, 20) + '...');

      // ✅ NOVO: Simular em modo desenvolvimento
      if (this.developmentMode) {
        console.log('🔧 DEVELOPMENT MODE: Simulating FCM notification');
        console.log('📋 Title:', notification.title);
        console.log('📋 Body:', notification.body);
        console.log('📋 Data:', data);
        
        return {
          success: true,
          messageId: 'dev-simulation-' + Date.now(),
          timestamp: new Date().toISOString(),
          developmentMode: true,
          note: 'This notification was simulated in development mode'
        };
      }

      // Construir mensagem seguindo a documentação oficial
      const message = {
        token: token,
        notification: {
          title: notification.title,
          body: notification.body,
          imageUrl: notification.image || undefined
        },
        data: {
          // Converter todos os valores para string (obrigatório no FCM)
          ...Object.fromEntries(
            Object.entries(data).map(([key, value]) => [key, String(value)])
          )
        },
        webpush: {
          headers: {
            TTL: webpush.ttl || '86400', // 24 horas
            Urgency: webpush.urgency || 'normal'
          },
          notification: {
            title: notification.title,
            body: notification.body,
            icon: webpush.icon || '/pinpal-logo-icon.png',
            badge: webpush.badge || '/pinpal-logo-icon.png',
            tag: webpush.tag || 'pinpal-notification',
            requireInteraction: webpush.requireInteraction !== false,
            actions: webpush.actions || [
              {
                action: 'view',
                title: 'Ver'
              },
              {
                action: 'dismiss',
                title: 'Dispensar'
              }
            ],
            data: {
              url: webpush.clickUrl || 'http://localhost:5773',
              ...data
            }
          },
          fcmOptions: {
            link: webpush.clickUrl || 'http://localhost:5773'
          }
        }
      };

      // Enviar usando Firebase Admin SDK
      const messaging = this.getMessaging();
      const messageId = await messaging.send(message);

      console.log('✅ Notificação enviada com sucesso!');
      console.log('📋 Message ID:', messageId);

      return {
        success: true,
        messageId: messageId,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      console.error('❌ Erro no serviço FCM:', error);
      return {
        success: false,
        error: error.message,
        code: error.code || 'UNKNOWN_ERROR',
        details: error
      };
    }
  }

  /**
   * Enviar notificação para múltiplos tokens usando sendEachForMulticast
   * @param {string[]} tokens - Array de FCM registration tokens
   * @param {Object} notification - Dados da notificação
   * @param {Object} data - Dados customizados (opcional)
   */
  async sendMulticastNotification(tokens, notification, data = {}) {
    try {
      console.log(`📱 Enviando notificação multicast para ${tokens.length} tokens...`);

      // ✅ NOVO: Simular em modo desenvolvimento
      if (this.developmentMode) {
        console.log('🔧 DEVELOPMENT MODE: Simulating multicast FCM notification');
        console.log('📋 Title:', notification.title);
        console.log('📋 Body:', notification.body);
        console.log('📋 Tokens:', tokens.length);
        
        return {
          success: true,
          totalCount: tokens.length,
          successCount: tokens.length,
          failureCount: 0,
          developmentMode: true,
          responses: tokens.map((token, index) => ({
            token: token.substring(0, 20) + '...',
            success: true,
            messageId: `dev-simulation-${index}-${Date.now()}`,
            error: null
          }))
        };
      }

      const message = {
        notification: {
          title: notification.title,
          body: notification.body,
          imageUrl: notification.image || undefined
        },
        data: {
          // Converter todos os valores para string
          ...Object.fromEntries(
            Object.entries(data).map(([key, value]) => [key, String(value)])
          )
        },
        tokens: tokens
      };

      const messaging = this.getMessaging();
      const response = await messaging.sendEachForMulticast(message);

      console.log(`📊 Multicast results: ${response.successCount} success, ${response.failureCount} failures`);

      return {
        success: true,
        totalCount: tokens.length,
        successCount: response.successCount,
        failureCount: response.failureCount,
        responses: response.responses.map((resp, index) => ({
          token: tokens[index].substring(0, 20) + '...',
          success: resp.success,
          messageId: resp.messageId || null,
          error: resp.error?.message || null
        }))
      };

    } catch (error) {
      console.error('❌ Erro no multicast:', error);
      return {
        success: false,
        error: error.message,
        code: error.code || 'UNKNOWN_ERROR'
      };
    }
  }

  /**
   * Validar token FCM
   * @param {string} token - FCM registration token
   */
  async validateToken(token) {
    try {
      console.log('🔍 Validating FCM token...');
      
      // ✅ NOVO: Simular em modo desenvolvimento
      if (this.developmentMode) {
        console.log('🔧 DEVELOPMENT MODE: Simulating token validation');
        return {
          success: true,
          valid: true,
          token: token.substring(0, 20) + '...',
          developmentMode: true,
          note: 'Token validation simulated in development mode'
        };
      }

      // Validação básica do formato do token
      if (!token || typeof token !== 'string' || token.length < 100) {
        return {
          success: true,
          valid: false,
          token: token ? token.substring(0, 20) + '...' : 'null',
          reason: 'Invalid token format'
        };
      }

      return {
        success: true,
        valid: true,
        token: token.substring(0, 20) + '...'
      };

    } catch (error) {
      console.error('❌ Error validating token:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Enviar notificação de teste
   * @param {string} token - FCM registration token
   */
  async sendTestNotification(token) {
    return this.sendNotification(
      token,
      {
        title: '🎉 PinPal Test Notification',
        body: 'This is a test notification from FCM REST API v1!'
      },
      {
        type: 'test',
        source: 'fcm-v1-service',
        timestamp: Date.now()
      },
      {
        clickUrl: 'http://localhost:5773',
        tag: 'pinpal-test'
      }
    );
  }

  /**
   * Obter status do serviço FCM
   */
  getStatus() {
    return {
      success: true,
      status: this.developmentMode ? 'development' : 'production',
      apiVersion: 'v1',
      projectId: this.projectId,
      serviceAccount: this.developmentMode ? 'development-mode' : 'configured',
      developmentMode: this.developmentMode
    };
  }
}

export default FCMv1Service;
